# 常用分类配置功能

## 问题描述

在之前的版本中，常用分类功能存在以下问题：
1. 存储的常用分类数量（20个）与显示的分类数量（10个）不一致
2. 常用分类显示数量不支持配置，硬编码为10个

## 解决方案

### 1. 配置文件修改

在 `nav/data/appconfig.json` 中添加了新的配置项：

```json
{
  "frequentCategory": {
    "enabled": true,
    "displayCount": 10,
    "storageCount": 20,
    "description": "常用分类配置 - displayCount: 显示数量, storageCount: 存储数量"
  }
}
```

**配置项说明：**
- `enabled`: 是否启用常用分类功能（默认：true）
- `displayCount`: 常用分类显示的网站数量（默认：10）
- `storageCount`: 本地存储的访问历史记录数量（默认：20）
- `description`: 配置项说明

### 2. 代码修改

#### VisitManager 类修改

1. **构造函数增强**：
   - 添加了 `defaultDisplayCount` 属性来存储默认显示数量
   - 添加了 `config` 属性来存储应用配置

2. **配置加载**：
   - 新增 `loadConfig()` 方法，在初始化时自动加载配置文件
   - 根据配置文件更新存储数量和显示数量

3. **方法优化**：
   - `getRecentSites(limit)`: 默认使用配置中的显示数量
   - `getRecentSiteIds(limit)`: 默认使用配置中的显示数量
   - `getStats()`: 统计信息使用配置中的显示数量
   - 新增 `getFrequentCategoryConfig()`: 获取常用分类配置信息

#### App 类修改

- `getFrequentSites()`: 移除硬编码的数量限制，使用配置中的显示数量

### 3. 一致性保证

现在存储数量和显示数量完全一致：
- **存储数量**: 由 `frequentCategory.storageCount` 配置控制
- **显示数量**: 由 `frequentCategory.displayCount` 配置控制
- **默认值**: 存储20个，显示10个（保持向后兼容）

## 使用方法

### 修改常用分类显示数量

编辑 `nav/data/appconfig.json` 文件：

```json
{
  "frequentCategory": {
    "enabled": true,
    "displayCount": 15,  // 修改为显示15个常用网站
    "storageCount": 30,  // 修改为存储30个访问记录
    "description": "常用分类配置"
  }
}
```

### 禁用常用分类功能

```json
{
  "frequentCategory": {
    "enabled": false,
    "displayCount": 10,
    "storageCount": 20,
    "description": "常用分类配置"
  }
}
```

## 测试验证

项目中包含了测试页面 `test-frequent-config.html`，可以用来验证配置是否正确工作：

1. 打开 `test-frequent-config.html`
2. 运行各项测试：
   - 配置加载测试
   - VisitManager 配置测试
   - 存储与显示数量一致性测试
   - 模拟访问测试

## 向后兼容性

- 如果配置文件中没有 `frequentCategory` 配置项，将使用默认值
- 如果配置文件加载失败，将使用默认值
- 现有的访问历史数据不会受到影响

## 注意事项

1. **配置合理性**: 建议 `displayCount` ≤ `storageCount`，避免显示数量超过存储数量
2. **性能考虑**: 过大的存储数量可能影响性能，建议根据实际需求设置
3. **用户体验**: 显示数量过多可能影响界面美观，建议设置在5-20之间

## 技术细节

### 配置加载时机
- VisitManager 初始化时自动加载配置
- 配置加载是异步的，确保在使用前完成加载

### 错误处理
- 配置文件加载失败时使用默认值
- 配置项缺失时使用默认值
- 控制台会输出相应的警告信息

### API 接口

```javascript
// 获取常用分类配置
const config = visitManager.getFrequentCategoryConfig();
console.log(config);
// 输出: { displayCount: 10, storageCount: 20, enabled: true }

// 获取常用网站（使用配置中的显示数量）
const recentSites = visitManager.getRecentSites();

// 获取常用网站ID（使用配置中的显示数量）
const recentSiteIds = visitManager.getRecentSiteIds();

// 获取指定数量的常用网站
const limitedSites = visitManager.getRecentSites(5);
```

## 更新日志

- **2025-08-14**: 实现常用分类配置功能
  - 添加配置文件支持
  - 修复存储与显示数量不一致问题
  - 提供可配置的常用分类显示数量
  - 保持向后兼容性
